#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查成本侧供应商数据
"""

import pymysql

DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

try:
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 检查成本侧供应商字段 ===')
    cursor.execute('SELECT 成本侧供应商, COUNT(*) FROM t_kuanbiao GROUP BY 成本侧供应商')
    results = cursor.fetchall()
    
    for row in results:
        supplier = row[0] if row[0] is not None else 'NULL'
        count = row[1]
        print(f'成本侧供应商: "{supplier}" - {count}条记录')
    
    print('\n=== 查看字段类型和长度 ===')
    cursor.execute('DESCRIBE t_kuanbiao')
    columns = cursor.fetchall()
    for col in columns:
        if '成本侧供应商' in col[0]:
            print(f'字段: {col[0]}, 类型: {col[1]}, 是否为空: {col[2]}, 键: {col[3]}, 默认值: {col[4]}, 额外: {col[5]}')
    
    print('\n=== 查看一些样本数据 ===')
    cursor.execute('SELECT 项目编码, 收入侧客户, 成本侧供应商, LENGTH(成本侧供应商) as len FROM t_kuanbiao LIMIT 10')
    samples = cursor.fetchall()
    
    for row in samples:
        print(f'项目: {row[0]}, 客户: {row[1]}, 供应商: "{row[2]}", 长度: {row[3]}')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'错误: {e}')
