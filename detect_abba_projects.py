#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
detect_abba_projects.py - 检测疑似ABBA项目关系脚本
功能：根据kuanbiao视图数据，识别"疑似ABBA"的项目关系

ABBA定义：
- A先作为供应商，B作为客户：A向B提供产品或服务
- 随后角色互换，B作为供应商，A作为客户：B向A提供产品或服务
- 在相同的所属行业（场景）下发生

当前分析逻辑（基于现有数据结构）：
1. 由于成本侧供应商字段为空，无法直接分析供应商-客户关系
2. 改为分析同一行业中有多个项目的客户，识别潜在的相互交易关系
3. 假设：在同一行业中都有多个项目的客户之间可能存在相互交易
4. 结果输出到dict_prj_abba表，标记为"潜在ABBA关系"
"""

import pymysql
import pandas as pd
from datetime import datetime
import sys
import traceback

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def connect_to_database():
    """连接到数据库"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print(f"[成功] 已连接到数据库: {DB_CONFIG['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接数据库失败: {e}")
        return None

def create_abba_table(conn):
    """创建dict_prj_abba表"""
    cursor = conn.cursor()
    try:
        # 删除已存在的表
        cursor.execute("DROP TABLE IF EXISTS dict_prj_abba")
        
        # 创建新表
        create_table_sql = """
        CREATE TABLE dict_prj_abba (
            id INT AUTO_INCREMENT PRIMARY KEY,
            场景分类 VARCHAR(100) COMMENT '所属行业/场景分类',
            企业A VARCHAR(500) COMMENT '企业A名称',
            企业B VARCHAR(500) COMMENT '企业B名称',
            A作为客户项目编码 TEXT COMMENT 'A作为客户的项目编码列表',
            A作为客户项目数量 INT COMMENT 'A作为客户的项目数量',
            B作为客户项目编码 TEXT COMMENT 'B作为客户的项目编码列表',
            B作为客户项目数量 INT COMMENT 'B作为客户的项目数量',
            ABBA风险等级 VARCHAR(20) COMMENT '风险等级：高/中/低',
            检测时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '检测时间',
            备注 TEXT COMMENT '备注信息'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='疑似ABBA项目关系表'
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        print("[成功] dict_prj_abba表创建完成")
        return True
        
    except Exception as e:
        print(f"[错误] 创建dict_prj_abba表失败: {e}")
        traceback.print_exc()
        return False
    finally:
        cursor.close()

def get_kuanbiao_data(conn):
    """获取kuanbiao表数据"""
    try:
        print("[信息] 正在获取kuanbiao表数据...")

        # 由于成本侧供应商字段为空，我们改用其他方式分析ABBA关系
        # 通过分析同一行业中不同项目的客户关系来识别潜在的ABBA模式
        query = """
        SELECT
            项目编码,
            收入侧客户,
            所属行业,
            项目名称,
            合同含税金额（万元）,
            项目经理,
            前向合同签约时间
        FROM t_kuanbiao
        WHERE 收入侧客户 IS NOT NULL
        AND 收入侧客户 != ''
        AND 所属行业 IS NOT NULL
        AND 所属行业 != ''
        """

        df = pd.read_sql(query, conn)
        print(f"[信息] 获取到 {len(df)} 条有效数据")
        return df

    except Exception as e:
        print(f"[错误] 获取kuanbiao数据失败: {e}")
        traceback.print_exc()
        return None

def detect_abba_relationships(df):
    """检测ABBA关系 - 基于客户数据分析潜在的相互交易关系"""
    print("[信息] 开始检测ABBA关系...")
    print("[说明] 由于成本侧供应商数据为空，将基于客户关系分析潜在的相互交易模式")

    abba_results = []

    # 按所属行业分组处理
    for industry in df['所属行业'].unique():
        if pd.isna(industry) or industry == '':
            continue

        industry_data = df[df['所属行业'] == industry].copy()
        print(f"[信息] 正在分析行业: {industry} ({len(industry_data)}条记录)")

        # 统计每个客户在该行业的项目数量
        customer_projects = {}

        for _, row in industry_data.iterrows():
            customer = row['收入侧客户']
            project_code = row['项目编码']

            if pd.notna(customer):
                if customer not in customer_projects:
                    customer_projects[customer] = []
                customer_projects[customer].append(project_code)

        # 查找在同一行业中有多个项目的客户（可能存在相互交易）
        multi_project_customers = {k: v for k, v in customer_projects.items() if len(v) >= 2}

        if len(multi_project_customers) >= 2:
            print(f"  - 发现 {len(multi_project_customers)} 个在{industry}行业有多个项目的客户")

            # 分析这些客户之间的潜在ABBA关系
            customers = list(multi_project_customers.keys())

            for i in range(len(customers)):
                for j in range(i + 1, len(customers)):
                    customer_a = customers[i]
                    customer_b = customers[j]

                    projects_a = multi_project_customers[customer_a]
                    projects_b = multi_project_customers[customer_b]

                    # 如果两个客户都在同一行业有多个项目，可能存在相互交易关系
                    total_projects = len(projects_a) + len(projects_b)

                    # 设定阈值：总项目数>=4且每个客户至少2个项目
                    if total_projects >= 4 and len(projects_a) >= 2 and len(projects_b) >= 2:

                        # 计算风险等级
                        if total_projects >= 8:
                            risk_level = "高"
                        elif total_projects >= 6:
                            risk_level = "中"
                        else:
                            risk_level = "低"

                        # 记录潜在ABBA关系
                        abba_result = {
                            '场景分类': industry,
                            '企业A': customer_a,
                            '企业B': customer_b,
                            'A作为客户项目编码': ','.join(projects_a),
                            'A作为客户项目数量': len(projects_a),
                            'B作为客户项目编码': ','.join(projects_b),
                            'B作为客户项目数量': len(projects_b),
                            'ABBA风险等级': risk_level,
                            '备注': f'在{industry}行业中发现两个客户都有多个项目，存在潜在相互交易可能性'
                        }

                        abba_results.append(abba_result)

                        print(f"[发现] 潜在ABBA关系: {customer_a} ↔ {customer_b} (行业: {industry}, 风险: {risk_level})")
                        print(f"  - {customer_a}: {len(projects_a)}个项目")
                        print(f"  - {customer_b}: {len(projects_b)}个项目")

    print(f"[完成] 共检测到 {len(abba_results)} 个潜在ABBA关系")
    return abba_results

def save_abba_results(conn, abba_results):
    """保存ABBA检测结果到数据库"""
    if not abba_results:
        print("[信息] 没有检测到ABBA关系，无需保存")
        return True
    
    cursor = conn.cursor()
    try:
        print(f"[信息] 正在保存 {len(abba_results)} 条ABBA检测结果...")
        
        insert_sql = """
        INSERT INTO dict_prj_abba (
            场景分类, 企业A, 企业B, 
            A作为客户项目编码, A作为客户项目数量,
            B作为客户项目编码, B作为客户项目数量,
            ABBA风险等级, 备注
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        for result in abba_results:
            cursor.execute(insert_sql, (
                result['场景分类'],
                result['企业A'],
                result['企业B'],
                result['A作为客户项目编码'],
                result['A作为客户项目数量'],
                result['B作为客户项目编码'],
                result['B作为客户项目数量'],
                result['ABBA风险等级'],
                result['备注']
            ))
        
        conn.commit()
        print(f"[成功] 已保存 {len(abba_results)} 条ABBA检测结果")
        return True
        
    except Exception as e:
        print(f"[错误] 保存ABBA结果失败: {e}")
        traceback.print_exc()
        conn.rollback()
        return False
    finally:
        cursor.close()

def generate_summary_report(conn):
    """生成汇总报告"""
    cursor = conn.cursor()
    try:
        print("\n" + "="*80)
        print("ABBA关系检测汇总报告")
        print("="*80)
        
        # 总体统计
        cursor.execute("SELECT COUNT(*) FROM dict_prj_abba")
        total_abba = cursor.fetchone()[0]
        print(f"检测到的ABBA关系总数: {total_abba}")
        
        if total_abba > 0:
            # 按风险等级统计
            print("\n按风险等级分布:")
            cursor.execute("SELECT ABBA风险等级, COUNT(*) FROM dict_prj_abba GROUP BY ABBA风险等级 ORDER BY COUNT(*) DESC")
            risk_stats = cursor.fetchall()
            for risk, count in risk_stats:
                print(f"  {risk}风险: {count}个")
            
            # 按行业统计
            print("\n按行业分布:")
            cursor.execute("SELECT 场景分类, COUNT(*) FROM dict_prj_abba GROUP BY 场景分类 ORDER BY COUNT(*) DESC")
            industry_stats = cursor.fetchall()
            for industry, count in industry_stats:
                print(f"  {industry}: {count}个")
            
            # 高风险ABBA关系详情
            print("\n高风险ABBA关系详情:")
            cursor.execute("SELECT 场景分类, 企业A, 企业B, A作为客户项目数量, B作为客户项目数量 FROM dict_prj_abba WHERE ABBA风险等级='高' ORDER BY (A作为客户项目数量 + B作为客户项目数量) DESC")
            high_risk = cursor.fetchall()
            
            if high_risk:
                for row in high_risk:
                    industry, company_a, company_b, count_a, count_b = row
                    print(f"  [{industry}] {company_a} ↔ {company_b} (项目数: {count_a}+{count_b}={count_a+count_b})")
            else:
                print("  无高风险ABBA关系")
        
        print("="*80)
        
    except Exception as e:
        print(f"[错误] 生成汇总报告失败: {e}")
    finally:
        cursor.close()

def main():
    """主函数"""
    print("="*80)
    print("疑似ABBA项目关系检测程序")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 连接数据库
    conn = connect_to_database()
    if not conn:
        print("[错误] 无法连接数据库，程序退出")
        sys.exit(1)
    
    try:
        # 创建结果表
        if not create_abba_table(conn):
            print("[错误] 创建结果表失败，程序退出")
            return
        
        # 获取数据
        df = get_kuanbiao_data(conn)
        if df is None or len(df) == 0:
            print("[警告] 没有获取到有效数据")
            return
        
        # 检测ABBA关系
        abba_results = detect_abba_relationships(df)
        
        # 保存结果
        if save_abba_results(conn, abba_results):
            # 生成汇总报告
            generate_summary_report(conn)
        
        print(f"\n程序执行完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"[错误] 程序执行失败: {e}")
        traceback.print_exc()
    finally:
        conn.close()
        print("[信息] 数据库连接已关闭")

if __name__ == "__main__":
    main()
